services:
  mongo-dev:
    image: mongo:8.0.8
    container_name: hostaudit-mongo-dev
    restart: unless-stopped
    env_file: .env
    ports:
      - '29017:27017'
    volumes:
      - mongo-data-dev:/data/db
    networks:
      - hostaudit-dev-network

  redis-dev:
    build:
      context: ./deployments/redis
    container_name: hostaudit-redis-dev
    restart: unless-stopped
    env_file: .env
    ports:
      - '26379:6379'
    volumes:
      - redis-data-dev:/data
    networks:
      - hostaudit-dev-network

volumes:
  mongo-data-dev:
    driver: local
  redis-data-dev:
    driver: local

networks:
  hostaudit-dev-network:
    driver: bridge
