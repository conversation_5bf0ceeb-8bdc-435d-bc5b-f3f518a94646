package logger

import (
	"net/http"
	"runtime"

	"github.com/go-chi/chi/v5/middleware"

	"jiansec/hostaudit/internal/config"
)

// Logger 返回 Chi 中间件日志记录器
func MiddlewareLogger(cfg *config.Config) func(next http.Handler) http.Handler {
	// 判断是否需要彩色输出
	color := shouldUseColor(cfg)
	logger := NewLogger(cfg)

	return middleware.RequestLogger(&middleware.DefaultLogFormatter{
		Logger:  logger,
		NoColor: !color,
	})
}

// shouldUseColor 判断是否应该使用彩色输出
func shouldUseColor(cfg *config.Config) bool {
	// Windows 系统通常不支持彩色输出
	if runtime.GOOS == "windows" {
		return false
	}

	// 只有输出到终端时才使用彩色
	return cfg.ServerLog == "stdout" || cfg.ServerLog == "stderr"
}
