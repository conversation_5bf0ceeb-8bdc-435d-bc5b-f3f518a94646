package logger

import (
	"io"
	"log"
	"os"

	"gopkg.in/natefinch/lumberjack.v2"

	"jiansec/hostaudit/internal/config"
)

// Setup 初始化日志记录器
func Setup(cfg *config.Config) {
	w := LogWriter(cfg)
	log.New(w, "", log.LstdFlags)
	log.SetOutput(w)
	log.SetFlags(log.LstdFlags)
}

// LogWriter 根据配置返回日志写入器
func LogWriter(cfg *config.Config) io.Writer {
	switch cfg.ServerLog {
	case "stdout":
		return os.Stdout
	case "stderr":
		return os.Stderr
	case "":
		return io.Discard
	default:
		// 文件日志，使用 lumberjack 进行日志轮转
		return &lumberjack.Logger{
			Filename:   cfg.ServerLog,
			MaxSize:    100, // MB
			MaxAge:     30,  // days
			MaxBackups: 10,
			LocalTime:  true,
			Compress:   true,
		}
	}
}

// NewLogger 创建新的日志记录器
func NewLogger(cfg *config.Config) *log.Logger {
	w := LogWriter(cfg)
	return log.New(w, "", log.LstdFlags)
}
