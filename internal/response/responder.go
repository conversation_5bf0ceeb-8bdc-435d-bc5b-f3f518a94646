package response

import (
	"encoding/json"
	"net/http"
	"strings"
)

// Responder 定义响应接口
type Responder interface {
	RespondJSON(w http.ResponseWriter, status ApiStatus, data interface{}, msg ...string)
	RespondSuccess(w http.ResponseWriter, data interface{}, msg ...string)
	RespondUnauthorized(w http.ResponseWriter, msg ...string)
	RespondForbidden(w http.ResponseWriter, msg ...string)
	RespondNotFound(w http.ResponseWriter, msg ...string)
	RespondError(w http.ResponseWriter, msg ...string)
	RespondSystemError(w http.ResponseWriter, msg ...string)
}

// DefaultResponder 默认响应器实现
type DefaultResponder struct{}

// NewDefaultResponder 创建默认响应器
func NewDefaultResponder() Responder {
	return &DefaultResponder{}
}

// RespondJSON 发送 JSON 响应
func (r *DefaultResponder) RespondJSON(w http.ResponseWriter, status ApiStatus, data interface{}, msg ...string) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	message := getResponseMessage(status, msg...)

	if err := json.NewEncoder(w).Encode(Response{
		Data: data,
		Meta: Meta{
			Status: status,
			Msg:    message,
		},
	}); err != nil {
		// 如果编码失败，尝试发送系统错误
		r.respondSystemErrorFallback(w)
	}
}

func (r *DefaultResponder) RespondSuccess(w http.ResponseWriter, data interface{}, msg ...string) {
	r.RespondJSON(w, StatusSuccess, data, msg...)
}

func (r *DefaultResponder) RespondUnauthorized(w http.ResponseWriter, msg ...string) {
	r.RespondJSON(w, StatusUnauthorized, nil, msg...)
}

func (r *DefaultResponder) RespondForbidden(w http.ResponseWriter, msg ...string) {
	r.RespondJSON(w, StatusForbidden, nil, msg...)
}

func (r *DefaultResponder) RespondNotFound(w http.ResponseWriter, msg ...string) {
	r.RespondJSON(w, StatusNotFound, nil, msg...)
}

func (r *DefaultResponder) RespondError(w http.ResponseWriter, msg ...string) {
	r.RespondJSON(w, StatusError, nil, msg...)
}

func (r *DefaultResponder) RespondSystemError(w http.ResponseWriter, msg ...string) {
	r.RespondJSON(w, StatusSystemError, nil, msg...)
}

// respondSystemErrorFallback 系统错误回退响应
func (r *DefaultResponder) respondSystemErrorFallback(w http.ResponseWriter) {
	// 这里使用简单的字符串响应，避免再次编码失败
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	w.Write([]byte(`{"data":null,"meta":{"status":500,"msg":"系统错误"}}`))
}

// getResponseMessage 获取响应消息
func getResponseMessage(status ApiStatus, msg ...string) string {
	if len(msg) > 0 {
		return strings.Join(msg, "\n")
	}
	return DefaultApiStatusMessage[status]
}
