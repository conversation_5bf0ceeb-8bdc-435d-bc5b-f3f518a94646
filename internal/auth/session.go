package auth

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"

	"jiansec/hostaudit/internal/storage"
	"jiansec/hostaudit/internal/users"
	"jiansec/hostaudit/internal/utils"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
)

// SessionManager 会话管理器
type SessionManager struct {
	config      *SessionConfig
	redisClient *redis.Client
	jwtManager  *JWTManager
	storage     *storage.Storage
}

// NewSessionManager 创建会话管理器
func NewSessionManager(config *SessionConfig, redisClient *redis.Client, jwtManager *JWTManager, storage *storage.Storage) *SessionManager {
	if redisClient == nil {
		panic("redisClient cannot be nil")
	}
	if jwtManager == nil {
		panic("jwtManager cannot be nil")
	}
	if storage == nil {
		panic("storage cannot be nil")
	}
	if config == nil {
		config = DefaultSessionConfig()
	}
	return &SessionManager{
		config:      config,
		redisClient: redisClient,
		jwtManager:  jwtManager,
		storage:     storage,
	}
}

// SessionConfig 会话配置
type SessionConfig struct {
	// 会话过期时间
	SessionTTL time.Duration
	// 刷新令牌过期时间
	RefreshTTL time.Duration
	// 最大并发会话数
	MaxConcurrentSessions int
	// 是否允许多设备登录
	AllowMultiDevice bool
	// 会话前缀
	SessionPrefix string
	// 刷新令牌前缀
	RefreshPrefix string
	// 用户会话列表前缀
	UserSessionsPrefix string
}

// DefaultSessionConfig 默认会话配置
func DefaultSessionConfig() *SessionConfig {
	return &SessionConfig{
		SessionTTL:            24 * time.Hour,
		RefreshTTL:            7 * 24 * time.Hour,
		MaxConcurrentSessions: 5,
		AllowMultiDevice:      false,
		SessionPrefix:         "session:",
		RefreshPrefix:         "refresh:",
		UserSessionsPrefix:    "user_sessions:",
	}
}

// DeviceInfo 设备信息
type DeviceInfo struct {
	DeviceID   string
	DeviceType string
	IP         string
	UserAgent  string
}

func GetDeviceInfo(r *http.Request) *DeviceInfo {
	userAgent := utils.GetDeviceUserAgent(r)
	deviceType := utils.DetectDeviceType(userAgent)
	deviceID := utils.GetDeviceID(r)
	if deviceID == "" {
		deviceID = utils.GenerateDeviceID()
	}

	return &DeviceInfo{
		DeviceID:   deviceID,
		DeviceType: deviceType,
		IP:         utils.GetDeviceIP(r),
		UserAgent:  userAgent,
	}
}

// Session 会话信息
type Session struct {
	SessionID    string    `json:"session_id"`
	RefreshToken string    `json:"refresh_token"`
	UserID       string    `json:"user_id"`
	DeviceID     string    `json:"device_id"`   // DeviceInfo.DeviceID 用于标识设备，如浏览器指纹、移动设备IMEI等
	DeviceType   string    `json:"device_type"` // DeviceInfo.DeviceType 用于标识设备类型，如浏览器、移动设备等
	IP           string    `json:"ip"`          // DeviceInfo.IP 用于标识设备 IP
	UserAgent    string    `json:"user_agent"`  // DeviceInfo.UserAgent 用于标识设备用户代理
	CreatedAt    time.Time `json:"created_at"`
	LastActiveAt time.Time `json:"last_active_at"`
	ExpiresAt    time.Time `json:"expires_at"`
}

// TokenPair 令牌对
type TokenPair struct {
	AccessToken  string    `json:"access_token"`
	RefreshToken string    `json:"refresh_token"`
	ExpiresAt    time.Time `json:"expires_at"`
}

// GetConfig 获取配置（供外部访问）
func (sm *SessionManager) GetConfig() *SessionConfig {
	return sm.config
}

// CreateSession 创建会话
func (sm *SessionManager) CreateSession(ctx context.Context, user *users.User, deviceInfo *DeviceInfo) (*TokenPair, error) {
	// 检查用户状态
	if user.Status != users.StatusActive {
		return nil, fmt.Errorf("user account is not active: %s", user.Status)
	}

	// 生成会话ID
	sessionID := generateSessionID()
	// 生成刷新令牌
	refreshToken := generateRefreshToken()

	// 创建会话信息
	session := &Session{
		SessionID:    sessionID,
		RefreshToken: refreshToken,
		UserID:       user.ID,
		DeviceID:     deviceInfo.DeviceID,
		DeviceType:   deviceInfo.DeviceType,
		IP:           deviceInfo.IP,
		UserAgent:    deviceInfo.UserAgent,
		CreatedAt:    time.Now(),
		LastActiveAt: time.Now(),
		ExpiresAt:    time.Now().Add(sm.config.SessionTTL),
	}

	// 检查并发会话限制
	if err := sm.checkConcurrentSessions(ctx, user.ID); err != nil {
		return nil, err
	}

	// 生成 JWT
	accessToken, err := sm.jwtManager.GenerateTokenWithSession(user.ID, sessionID)
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}

	// 保存会话到 Redis
	sessionKey := sm.getSessionKey(sessionID)
	sessionData, err := json.Marshal(session)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal session: %w", err)
	}

	// 使用事务保存会话信息
	pipe := sm.redisClient.TxPipeline()

	// 保存会话
	pipe.Set(ctx, sessionKey, sessionData, sm.config.SessionTTL)

	// 保存刷新令牌
	refreshKey := sm.getRefreshKey(refreshToken)
	pipe.Set(ctx, refreshKey, sessionID, sm.config.RefreshTTL)

	// 添加到用户会话列表
	userSessionsKey := sm.getUserSessionsKey(user.ID)
	pipe.SAdd(ctx, userSessionsKey, sessionID)
	pipe.Expire(ctx, userSessionsKey, sm.config.RefreshTTL)

	if _, err := pipe.Exec(ctx); err != nil {
		return nil, fmt.Errorf("failed to save session: %w", err)
	}

	return &TokenPair{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresAt:    session.ExpiresAt,
	}, nil
}

// GetOrCreateSession 获取现有会话或创建新会话
// 此方法会：
// 1. 查找用户是否有相同设备的有效会话
// 2. 如果有，更新设备信息并生成新令牌对
// 3. 如果没有，创建新会话
func (sm *SessionManager) GetOrCreateSession(ctx context.Context, user *users.User, deviceInfo *DeviceInfo) (*TokenPair, error) {
	// 检查用户是否已有会话
	existingSessions, err := sm.GetUserSessions(ctx, user.ID)
	if err != nil {
		// 获取会话失败不是致命错误，继续创建新会话
		log.Printf("GetOrCreateSession: Failed to get user sessions for user %s: %v, creating new session", user.ID, err)
		return sm.CreateSession(ctx, user, deviceInfo)
	}

	// 查找是否有相同设备的有效会话
	existingSession := sm.findValidSessionByDevice(ctx, existingSessions, deviceInfo)

	if existingSession != nil {
		// 相同设备已有有效会话，更新设备信息并生成新令牌对
		log.Printf("GetOrCreateSession: Found existing session for user %s device %s, refreshing",
			user.ID, deviceInfo.DeviceID)

		// 更新会话的设备信息（IP、UserAgent 等可能变化）
		if err := sm.updateSessionDeviceInfo(ctx, existingSession.SessionID, deviceInfo); err != nil {
			log.Printf("GetOrCreateSession: Failed to update session device info: %v, continuing", err)
		}

		// 生成新的令牌对
		tokenPair, err := sm.GenerateNewTokenPair(ctx, existingSession.SessionID, deviceInfo)
		if err != nil {
			log.Printf("GetOrCreateSession: Failed to generate new token pair: %v, creating new session", err)
			// 如果生成新令牌失败，创建新会话
			return sm.CreateSession(ctx, user, deviceInfo)
		}

		return tokenPair, nil
	}

	// 没有相同设备的有效会话，创建新会话
	log.Printf("GetOrCreateSession: Creating new session for user %s device %s",
		user.ID, deviceInfo.DeviceID)

	return sm.CreateSession(ctx, user, deviceInfo)
}

// ValidateSession 验证会话
func (sm *SessionManager) ValidateSession(ctx context.Context, sessionID string, deviceInfo *DeviceInfo) (*Session, error) {
	sessionKey := sm.getSessionKey(sessionID)

	// 获取会话数据
	sessionData, err := sm.redisClient.Get(ctx, sessionKey).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, fmt.Errorf("session not found")
		}
		return nil, fmt.Errorf("failed to get session: %w", err)
	}

	var session Session
	if err := json.Unmarshal([]byte(sessionData), &session); err != nil {
		return nil, fmt.Errorf("failed to unmarshal session: %w", err)
	}

	// 检查会话是否过期
	if time.Now().After(session.ExpiresAt) {
		sm.DeleteSession(ctx, sessionID)
		return nil, fmt.Errorf("session expired")
	}

	// 检查设备信息
	if session.DeviceID != deviceInfo.DeviceID {
		sm.DeleteSession(ctx, sessionID)
		return nil, fmt.Errorf("invalid device")
	}

	// 更新最后活跃时间（避免频繁写入，只有超过5分钟才更新）
	now := time.Now()
	if now.Sub(session.LastActiveAt) > 5*time.Minute {
		session.LastActiveAt = now
		sm.updateSessionActivity(ctx, sessionID, &session)
	}

	// 检测IP变化并记录
	if session.IP != deviceInfo.IP {
		log.Printf("ValidateSession: Session %s IP changed from %s to %s", sessionID, session.IP, deviceInfo.IP)
		sm.updateSessionDeviceInfo(ctx, sessionID, deviceInfo)
	}

	return &session, nil
}

// RefreshSession 刷新会话
func (sm *SessionManager) RefreshSession(ctx context.Context, refreshToken string, deviceInfo *DeviceInfo) (*TokenPair, error) {
	refreshKey := sm.getRefreshKey(refreshToken)

	// 获取会话ID
	sessionID, err := sm.redisClient.Get(ctx, refreshKey).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, fmt.Errorf("invalid refresh token")
		}
		return nil, fmt.Errorf("failed to get refresh token: %w", err)
	}

	// 验证会话
	session, err := sm.ValidateSession(ctx, sessionID, deviceInfo)
	if err != nil {
		return nil, err
	}

	// 生成新的令牌对
	newAccessToken, err := sm.jwtManager.GenerateTokenWithSession(session.UserID, sessionID)
	if err != nil {
		return nil, fmt.Errorf("failed to generate new access token: %w", err)
	}

	newRefreshToken := generateRefreshToken()

	// 更新会话过期时间
	session.RefreshToken = newRefreshToken
	session.ExpiresAt = time.Now().Add(sm.config.SessionTTL)
	sessionData, err := json.Marshal(session)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal session: %w", err)
	}

	// 更新 Redis
	pipe := sm.redisClient.TxPipeline()
	pipe.Del(ctx, refreshKey)
	pipe.Set(ctx, sm.getSessionKey(sessionID), sessionData, sm.config.SessionTTL)
	pipe.Set(ctx, sm.getRefreshKey(newRefreshToken), sessionID, sm.config.RefreshTTL)

	if _, err := pipe.Exec(ctx); err != nil {
		return nil, fmt.Errorf("failed to update session: %w", err)
	}

	return &TokenPair{
		AccessToken:  newAccessToken,
		RefreshToken: newRefreshToken,
		ExpiresAt:    session.ExpiresAt,
	}, nil
}

// DeleteSession 删除会话
func (sm *SessionManager) DeleteSession(ctx context.Context, sessionID string) error {
	// 获取会话信息
	session, err := sm.getSessionInfo(ctx, sessionID)
	if err != nil {
		return err
	}

	// 删除会话和相关数据
	pipe := sm.redisClient.TxPipeline()
	pipe.Del(ctx, sm.getSessionKey(sessionID))
	pipe.Del(ctx, sm.getRefreshKey(session.RefreshToken))
	pipe.SRem(ctx, sm.getUserSessionsKey(session.UserID), sessionID)

	if _, err := pipe.Exec(ctx); err != nil {
		return fmt.Errorf("failed to delete session: %w", err)
	}

	return nil
}

// DeleteAllUserSessions 删除用户所有会话
func (sm *SessionManager) DeleteAllUserSessions(ctx context.Context, userID string) error {
	userSessionsKey := sm.getUserSessionsKey(userID)

	// 获取用户所有会话
	sessionIDs, err := sm.redisClient.SMembers(ctx, userSessionsKey).Result()
	if err != nil {
		return fmt.Errorf("failed to get user sessions: %w", err)
	}

	if len(sessionIDs) == 0 {
		return nil
	}

	// 批量删除会话
	pipe := sm.redisClient.TxPipeline()
	for _, sessionID := range sessionIDs {
		session, err := sm.getSessionInfo(ctx, sessionID)
		if err != nil {
			// 忽略无效会话
			continue
		}
		pipe.Del(ctx, sm.getRefreshKey(session.RefreshToken))
		pipe.Del(ctx, sm.getSessionKey(sessionID))
	}
	pipe.Del(ctx, userSessionsKey)

	if _, err := pipe.Exec(ctx); err != nil {
		return fmt.Errorf("failed to delete user sessions: %w", err)
	}

	return nil
}

// GetUserSessions 获取用户所有会话
func (sm *SessionManager) GetUserSessions(ctx context.Context, userID string) ([]*Session, error) {
	userSessionsKey := sm.getUserSessionsKey(userID)

	// 获取会话ID列表
	sessionIDs, err := sm.redisClient.SMembers(ctx, userSessionsKey).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get user sessions: %w", err)
	}

	var sessions []*Session
	for _, sessionID := range sessionIDs {
		session, err := sm.getSessionInfo(ctx, sessionID)
		if err != nil {
			// 忽略无效会话
			continue
		}
		sessions = append(sessions, session)
	}

	return sessions, nil
}

// updateSessionDeviceInfo 更新会话的设备信息
func (sm *SessionManager) updateSessionDeviceInfo(ctx context.Context, sessionID string, deviceInfo *DeviceInfo) error {
	session, err := sm.getSessionInfo(ctx, sessionID)
	if err != nil {
		return err
	}

	// 更新设备信息
	session.IP = deviceInfo.IP
	session.UserAgent = deviceInfo.UserAgent
	session.LastActiveAt = time.Now()

	// 保存更新后的会话
	sessionData, err := json.Marshal(session)
	if err != nil {
		return fmt.Errorf("failed to marshal updated session: %w", err)
	}

	sessionKey := sm.getSessionKey(sessionID)
	if err := sm.redisClient.Set(ctx, sessionKey, sessionData, sm.config.SessionTTL).Err(); err != nil {
		return fmt.Errorf("failed to update session: %w", err)
	}

	return nil
}

// checkConcurrentSessions 检查并发会话限制
func (sm *SessionManager) checkConcurrentSessions(ctx context.Context, userID string) error {
	// 不允许多设备登录
	if !sm.config.AllowMultiDevice {
		// 删除所有会话
		log.Printf("checkConcurrentSessions: Deleting all existing sessions for user %s", userID)
		err := sm.DeleteAllUserSessions(ctx, userID)
		if err != nil {
			log.Printf("checkConcurrentSessions: Failed to delete all user sessions: %v", err)
			return err
		}
		return nil
	}

	// 检查会话数量限制
	userSessionsKey := sm.getUserSessionsKey(userID)
	count, err := sm.redisClient.SCard(ctx, userSessionsKey).Result()
	if err != nil {
		return fmt.Errorf("failed to count user sessions: %w", err)
	}

	if count >= int64(sm.config.MaxConcurrentSessions) {
		// 删除最旧的会话
		sessions, err := sm.GetUserSessions(ctx, userID)
		if err != nil {
			return err
		}

		if len(sessions) == 0 {
			// 如果没有会话但计数不为0，可能是数据不一致，清理用户会话列表
			log.Printf("checkConcurrentSessions: Session count mismatch for user %s, cleaning up", userID)
			session, err := sm.getSessionInfo(ctx, userSessionsKey)
			if err != nil {
				return err
			}
			err = sm.DeleteSession(ctx, session.SessionID)
			if err != nil {
				return err
			}
			return nil
		}

		// 按创建时间排序，找到最旧的会话
		oldestSession := sessions[0]
		for _, s := range sessions {
			if s.CreatedAt.Before(oldestSession.CreatedAt) {
				oldestSession = s
			}
		}

		// 删除最旧的会话
		if err := sm.DeleteSession(ctx, oldestSession.SessionID); err != nil {
			return err
		}
	}

	return nil
}

// GenerateNewTokenPair 为现有会话生成新的令牌对
func (sm *SessionManager) GenerateNewTokenPair(ctx context.Context, sessionID string, deviceInfo *DeviceInfo) (*TokenPair, error) {
	session, err := sm.ValidateSession(ctx, sessionID, deviceInfo)
	if err != nil {
		return nil, err
	}

	// 生成新的访问令牌
	accessToken, err := sm.jwtManager.GenerateTokenWithSession(session.UserID, sessionID)
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}

	// 生成新的刷新令牌
	newRefreshToken := generateRefreshToken()

	// 更新会话过期时间和刷新令牌
	session.RefreshToken = newRefreshToken
	session.ExpiresAt = time.Now().Add(sm.config.SessionTTL)
	sessionData, err := json.Marshal(session)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal session: %w", err)
	}

	// 更新 Redis
	pipe := sm.redisClient.TxPipeline()
	pipe.Del(ctx, sm.getRefreshKey(session.RefreshToken))
	pipe.Set(ctx, sm.getSessionKey(sessionID), sessionData, sm.config.SessionTTL)
	pipe.Set(ctx, sm.getRefreshKey(newRefreshToken), sessionID, sm.config.RefreshTTL)

	if _, err := pipe.Exec(ctx); err != nil {
		return nil, fmt.Errorf("failed to update session tokens: %w", err)
	}

	return &TokenPair{
		AccessToken:  accessToken,
		RefreshToken: newRefreshToken,
		ExpiresAt:    session.ExpiresAt,
	}, nil
}

// Helper functions
func (sm *SessionManager) getSessionKey(sessionID string) string {
	return sm.config.SessionPrefix + sessionID
}

func (sm *SessionManager) getRefreshKey(refreshToken string) string {
	return sm.config.RefreshPrefix + refreshToken
}

func (sm *SessionManager) getUserSessionsKey(userID string) string {
	return sm.config.UserSessionsPrefix + userID
}

func (sm *SessionManager) getSessionInfo(ctx context.Context, sessionID string) (*Session, error) {
	sessionKey := sm.getSessionKey(sessionID)
	sessionData, err := sm.redisClient.Get(ctx, sessionKey).Result()
	if err != nil {
		return nil, err
	}

	var session Session
	if err := json.Unmarshal([]byte(sessionData), &session); err != nil {
		return nil, err
	}

	return &session, nil
}

func (sm *SessionManager) updateSessionActivity(ctx context.Context, sessionID string, session *Session) {
	sessionData, err := json.Marshal(session)
	if err != nil {
		log.Printf("updateSessionActivity: Failed to marshal session: %v", err)
		return
	}
	if err := sm.redisClient.Set(ctx, sm.getSessionKey(sessionID), sessionData, sm.config.SessionTTL).Err(); err != nil {
		log.Printf("updateSessionActivity: Failed to update session: %v", err)
	}
}

// findValidSessionByDevice 查找指定设备的有效会话
func (sm *SessionManager) findValidSessionByDevice(ctx context.Context, sessions []*Session, deviceInfo *DeviceInfo) *Session {
	for _, session := range sessions {
		if session.DeviceID == deviceInfo.DeviceID {
			// 验证会话是否有效
			validSession, err := sm.ValidateSession(ctx, session.SessionID, deviceInfo)
			if err == nil {
				return validSession
			}
			// 如果会话无效，继续查找其他会话
			log.Printf("findValidSessionByDevice: Session %s for device %s is invalid: %v",
				session.SessionID, deviceInfo.DeviceID, err)
		}
	}
	return nil
}

// 辅助函数
func generateSessionID() string {
	return uuid.New().String()
}

func generateRefreshToken() string {
	// 使用两个UUID拼接生成更长的刷新令牌
	return uuid.New().String() + uuid.New().String()
}
