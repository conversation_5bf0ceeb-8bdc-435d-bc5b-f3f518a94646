package auth

import (
	"context"
	"log"
	"net/http"
	"strings"

	"jiansec/hostaudit/internal/response"
	"jiansec/hostaudit/internal/storage"
	"jiansec/hostaudit/internal/users"
)

// 默认响应器
var defaultResponder = response.NewDefaultResponder()

// contextKey 上下文键
type contextKey string

const (
	userContextKey    contextKey = "user"    // 用于存储用户信息的上下文键
	sessionContextKey contextKey = "session" // 用于存储会话信息的上下文键
)

// SessionAuthConfig 会话认证配置
type SessionAuthConfig struct {
	SessionManager *SessionManager    // 会话管理器
	Responder      response.Responder // Responder 响应器（可选）
	SkipPaths      []string           // SkipPaths 跳过认证的路径（可选）
}

// MiddlewareSessionAuth 创建 JWT 认证中间件
func MiddlewareSessionAuth(config SessionAuthConfig) func(http.Handler) http.Handler {
	// 如果没有提供响应器，使用默认响应器
	if config.Responder == nil {
		config.Responder = defaultResponder
	}
	// 如果会话管理器为空，返回一个中间件，该中间件会返回 500 错误
	if config.SessionManager == nil {
		log.Println("MiddlewareSessionAuth: SessionManager is nil")
		return func(next http.Handler) http.Handler {
			return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				config.Responder.RespondSystemError(w, "SessionManager is nil")
			})
		}
	}

	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// 检查是否需要跳过认证
			if shouldSkipAuth(r.URL.Path, config.SkipPaths) {
				next.ServeHTTP(w, r)
				return
			}

			// 从请求头获取 token
			authHeader := r.Header.Get("Authorization")
			if authHeader == "" {
				config.Responder.RespondUnauthorized(w, "Missing authorization header")
				return
			}

			// 检查 Bearer 前缀
			parts := strings.Split(authHeader, " ")
			if len(parts) != 2 || parts[0] != "Bearer" {
				config.Responder.RespondUnauthorized(w, "Invalid authorization header format")
				return
			}

			tokenString := parts[1]

			// 验证 JWT
			claims, err := config.SessionManager.jwtManager.ValidateToken(tokenString)
			if err != nil {
				config.Responder.RespondUnauthorized(w, "Invalid or expired token")
				return
			}

			// 获取设备信息
			deviceInfo := GetDeviceInfo(r)

			// 验证会话
			session, err := config.SessionManager.ValidateSession(r.Context(), claims.SessionID, deviceInfo)
			if err != nil {
				log.Printf("MiddlewareSessionAuth: ValidateSession Err: %v", err)
				config.Responder.RespondUnauthorized(w, "Invalid session")
				return
			}

			// 将会话信息存储到上下文中
			ctx := ContextWithSession(r.Context(), session)
			next.ServeHTTP(w, r.WithContext(ctx))
		})
	}
}

// RequireRoles 创建角色验证中间件，用于检查用户是否具有所需的角色
func RequireRoles(storage *storage.Storage, responder response.Responder, roles ...users.Role) func(http.Handler) http.Handler {
	if responder == nil {
		responder = defaultResponder
	}

	requiredRoles := make(map[users.Role]bool)
	for _, role := range roles {
		requiredRoles[role] = true
	}

	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			session, ok := GetSessionFromContext(r.Context())
			if !ok {
				responder.RespondUnauthorized(w)
				return
			}

			user, err := storage.Users.GetByID(r.Context(), session.UserID)
			if err != nil {
				responder.RespondSystemError(w, "Failed to get user")
				return
			}

			if user == nil {
				responder.RespondUnauthorized(w)
				return
			}

			// 检查用户是否有所需的角色
			if requiredRoles[user.Role] {
				next.ServeHTTP(w, r)
				return
			}

			responder.RespondForbidden(w)
		})
	}
}

// ContextWithSession 将会话信息存储到上下文中
func ContextWithSession(ctx context.Context, session *Session) context.Context {
	return context.WithValue(ctx, sessionContextKey, session)
}

// GetSessionFromContext 从上下文中获取会话信息
func GetSessionFromContext(ctx context.Context) (*Session, bool) {
	session, ok := ctx.Value(sessionContextKey).(*Session)
	return session, ok
}

// shouldSkipAuth 检查路径是否需要跳过认证
func shouldSkipAuth(path string, skipPaths []string) bool {
	for _, skipPath := range skipPaths {
		if strings.HasPrefix(path, skipPath) {
			return true
		}
	}
	return false
}
