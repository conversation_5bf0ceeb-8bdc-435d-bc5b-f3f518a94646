package server

import (
	"net/http"
	"time"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/chi/v5/middleware"
	"github.com/redis/go-redis/v9"

	"jiansec/hostaudit/internal/app/handlers"
	"jiansec/hostaudit/internal/auth"
	"jiansec/hostaudit/internal/config"
	"jiansec/hostaudit/internal/logger"
	"jiansec/hostaudit/internal/storage"
)

// NewRouter 创建路由
func NewRouter(cfg *config.Config, storage *storage.Storage, redisClient *redis.Client) http.Handler {
	r := chi.NewRouter()

	// 基础中间件
	r.Use(middleware.RequestID)
	r.Use(middleware.RealIP)
	r.Use(logger.MiddlewareLogger(cfg))
	r.Use(middleware.Recoverer)
	r.Use(middleware.Timeout(60 * time.Second))
	r.Use(middleware.NoCache) // 禁用缓存
	r.Use(MiddlewareCors(cfg))

	// 创建 JWT Manager
	jwtManager, err := auth.NewJWTManager(cfg.JWTSecret, cfg.JWTExpiry)
	if err != nil {
		panic(err)
	}

	// 创建 Session Manager
	sessionManager := auth.NewSessionManager(auth.DefaultSessionConfig(), redisClient, jwtManager, storage)

	// 创建处理器
	h := handlers.New(cfg, storage, sessionManager)

	// 创建认证中间件，使用与 handlers 相同的响应器
	authMiddleware := auth.MiddlewareSessionAuth(auth.SessionAuthConfig{
		SessionManager: sessionManager,
		Responder:      h, // 直接使用 handlers 作为响应器
		SkipPaths: []string{
			"/health",
			"/oauth",
			"/api/v1/auth/login",
		},
	})

	// 应用认证中间件
	r.Use(authMiddleware)

	// 健康检查
	r.Get("/health", h.Health)

	// OAuth 路由
	r.Get("/oauth/{provider}/login", h.OAuthLogin)
	r.Get("/oauth/{provider}/callback", h.OAuthCallback)

	// API 路由组
	r.Route("/api/v1", func(r chi.Router) {
		r.Get("/auth/login", h.Login)
		r.Post("/auth/logout", h.Logout)
		r.Post("/auth/logout-all", h.LogoutAll)
		r.Post("/auth/refresh-token", h.RefreshToken)

		r.Get("/profile", h.UserProfile)
	})

	// 静态文件服务 TODO

	return r
}
