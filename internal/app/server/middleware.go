package server

import (
	"net/http"

	"jiansec/hostaudit/internal/config"
)

// Cors 返回 Chi 中间件 CORS 处理器
func MiddlewareCors(cfg *config.Config) func(next http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// 开发环境下允许跨域请求
			if cfg.IsDevelopment() {
				w.Header().Set("Access-Control-Allow-Origin", "*")
				w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
				w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
				w.<PERSON><PERSON>().Set("Access-Control-Allow-Credentials", "true")
			}
			// OPTIONS 请求预检
			if r.Method == "OPTIONS" {
				w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE")
				w.<PERSON><PERSON>().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
				w.Write<PERSON>ead<PERSON>(http.StatusNoContent)
				return
			}
			next.ServeHTTP(w, r)
		})
	}
}
