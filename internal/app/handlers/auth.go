package handlers

import (
	"encoding/json"
	"net/http"

	"jiansec/hostaudit/internal/auth"
	"jiansec/hostaudit/internal/users"
)

// LoginRequest 登录请求
type LoginRequest struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

// Login 用户登录
func (h *Handlers) Login(w http.ResponseWriter, r *http.Request) {
	var req LoginRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		h.RespondError(w, "Invalid request body")
		return
	}

	// 验证用户凭据
	user, err := h.storage.Users.ValidateCredentials(r.Context(), req.Username, req.Password)
	if err != nil {
		h.RespondUnauthorized(w, "Invalid credentials")
		return
	}

	// 检查用户状态
	if user.Status != users.StatusActive {
		h.RespondForbidden(w, "Account is not active")
		return
	}

	// 获取设备信息
	deviceInfo := auth.GetDeviceInfo(r)

	// 使用封装的方法创建或获取会话
	tokenPair, err := h.sessionManager.GetOrCreateSession(r.Context(), user, deviceInfo)
	if err != nil {
		h.RespondError(w, "Failed to create session")
		return
	}

	// 设置 RefreshToken 到 Cookie
	http.SetCookie(w, &http.Cookie{
		Name:     "refresh_token",
		Value:    tokenPair.RefreshToken,
		Path:     "/",
		HttpOnly: true,
		Secure:   h.config.IsProduction(),
		SameSite: http.SameSiteLaxMode,
		Expires:  tokenPair.ExpiresAt,
	})

	w.Header().Set("X-Access-Token", tokenPair.AccessToken)
	h.RespondSuccess(w, map[string]interface{}{
		"token": tokenPair.AccessToken,
	})
}

// Logout 用户登出
func (h *Handlers) Logout(w http.ResponseWriter, r *http.Request) {
	// 从上下文获取会话信息
	session, ok := auth.GetSessionFromContext(r.Context())
	if !ok {
		h.RespondUnauthorized(w, "No active session")
		return
	}

	// 删除会话
	if err := h.sessionManager.DeleteSession(r.Context(), session.SessionID); err != nil {
		h.RespondError(w, "Failed to logout")
		return
	}

	h.RespondSuccess(w, nil, "退出成功")
}

// LogoutAll 登出所有设备
func (h *Handlers) LogoutAll(w http.ResponseWriter, r *http.Request) {
	// 从上下文获取会话信息
	session, ok := auth.GetSessionFromContext(r.Context())
	if !ok {
		h.RespondUnauthorized(w, "No active session")
		return
	}

	// 删除用户所有会话
	if err := h.sessionManager.DeleteAllUserSessions(r.Context(), session.UserID); err != nil {
		h.RespondError(w, "Failed to logout from all devices")
		return
	}

	h.RespondSuccess(w, map[string]string{
		"message": "Successfully logged out from all devices",
	})
}

// RefreshToken 刷新访问令牌
func (h *Handlers) RefreshToken(w http.ResponseWriter, r *http.Request) {
	// 从 Cookie 获取 refresh token
	cookie, err := r.Cookie("refresh_token")
	if err != nil {
		h.RespondUnauthorized(w, "Refresh token not found")
		return
	}
	refreshToken := cookie.Value
	if refreshToken == "" {
		h.RespondUnauthorized(w, "Refresh token is required")
		return
	}

	// 获取设备信息
	deviceInfo := auth.GetDeviceInfo(r)

	// 刷新令牌对
	tokenPair, err := h.sessionManager.RefreshSession(r.Context(), refreshToken, deviceInfo)
	if err != nil {
		// 清除无效的 refresh token cookie
		http.SetCookie(w, &http.Cookie{
			Name:     "refresh_token",
			Value:    "",
			Path:     "/",
			HttpOnly: true,
			Secure:   true,
			SameSite: http.SameSiteLaxMode,
			MaxAge:   -1, // 立即过期
		})
		h.RespondUnauthorized(w, "Invalid refresh token")
		return
	}

	// 设置新的 RefreshToken 到 Cookie
	refreshCookie := &http.Cookie{
		Name:     "refresh_token",
		Value:    tokenPair.RefreshToken,
		Path:     "/",
		HttpOnly: true,
		Secure:   true,
		SameSite: http.SameSiteLaxMode,
		Expires:  tokenPair.ExpiresAt,
	}

	if h.config.IsDevelopment() {
		refreshCookie.Secure = false
	}

	http.SetCookie(w, refreshCookie)

	w.Header().Set("X-Access-Token", tokenPair.AccessToken)
	h.RespondSuccess(w, map[string]interface{}{
		"token": tokenPair.AccessToken,
	})
}

// GetSessions 获取当前用户的所有会话
func (h *Handlers) GetSessions(w http.ResponseWriter, r *http.Request) {
	// 从上下文获取会话信息
	session, ok := auth.GetSessionFromContext(r.Context())
	if !ok {
		h.RespondUnauthorized(w, "No active session")
		return
	}

	// 获取用户所有会话
	sessions, err := h.sessionManager.GetUserSessions(r.Context(), session.UserID)
	if err != nil {
		h.RespondError(w, "Failed to get sessions")
		return
	}

	// 标记当前会话
	sessionList := make([]map[string]interface{}, len(sessions))
	for i, s := range sessions {
		sessionList[i] = map[string]interface{}{
			"session_id":     s.SessionID,
			"device_type":    s.DeviceType,
			"ip":             s.IP,
			"user_agent":     s.UserAgent,
			"created_at":     s.CreatedAt,
			"last_active_at": s.LastActiveAt,
			"expires_at":     s.ExpiresAt,
			"is_current":     s.SessionID == session.SessionID,
		}
	}

	h.RespondSuccess(w, map[string]interface{}{
		"sessions": sessionList,
		"count":    len(sessions),
	})
}
