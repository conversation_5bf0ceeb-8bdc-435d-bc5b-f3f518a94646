package handlers

import (
	"net/http"

	"github.com/gorilla/sessions"

	"jiansec/hostaudit/internal/auth"
	"jiansec/hostaudit/internal/config"
	"jiansec/hostaudit/internal/oauth"
	"jiansec/hostaudit/internal/response"
	"jiansec/hostaudit/internal/storage"
)

// Handlers 处理器集合
type Handlers struct {
	response.Responder
	config         *config.Config
	storage        *storage.Storage
	SessionName    string
	cookieStore    *sessions.CookieStore
	oAuthManager   *oauth.OAuthManager
	sessionManager *auth.SessionManager
}

// New 创建处理器实例
func New(cfg *config.Config, storage *storage.Storage, sessionManager *auth.SessionManager) *Handlers {
	// 创建 Cookie Store
	cookieStore := sessions.NewCookieStore([]byte(cfg.SessionSecret))
	// 设置 Cookie Store 的安全选项
	cookieStore.Options = &sessions.Options{
		Path:     "/",                  // 全站有效
		MaxAge:   7 * 24 * 3600,        // 7 天
		HttpOnly: true,                 // 防止 XSS
		Secure:   cfg.IsProduction(),   // 生产环境下启用 Secure Cookie
		SameSite: http.SameSiteLaxMode, // 防止 CSRF 攻击
		Domain:   "",                   // 设置域名
	}

	// 创建 OAuthManager
	oAuthManager := oauth.NewOAuthManager(cfg)
	// 注册 Feishu OAuth 提供者
	oAuthManager.RegisterProvider(oauth.NewFeishuProvider(cfg))

	// 创建 Responder
	responder := response.NewDefaultResponder()

	handlers := &Handlers{
		Responder:      responder,
		config:         cfg,
		storage:        storage,
		SessionName:    "hostaudit_session",
		cookieStore:    cookieStore,
		oAuthManager:   oAuthManager,
		sessionManager: sessionManager,
	}

	return handlers
}

// Health 健康检查
func (h *Handlers) Health(w http.ResponseWriter, r *http.Request) {
	// 可以在这里添加更多健康检查逻辑，比如检查数据库连接
	h.RespondSuccess(w, map[string]interface{}{
		"status": "healthy",
		"env":    h.config.Env,
	})
}
