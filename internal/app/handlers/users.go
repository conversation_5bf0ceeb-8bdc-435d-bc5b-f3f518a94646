package handlers

import (
	"jiansec/hostaudit/internal/auth"
	"net/http"
)

// UserProfile 获取用户信息
func (h *Handlers) UserProfile(w http.ResponseWriter, r *http.Request) {
	session, ok := auth.GetSessionFromContext(r.Context())
	if !ok {
		h.RespondUnauthorized(w, "No active session")
		return
	}

	userID := session.UserID
	if userID == "" {
		h.RespondSystemError(w, "Failed to get user ID from session")
		return
	}

	// 从存储获取用户信息
	user, err := h.storage.Users.GetByID(r.Context(), userID)
	if err != nil {
		h.RespondSystemError(w, "Failed to get user profile")
		return
	}

	h.RespondSuccess(w, user)
}
