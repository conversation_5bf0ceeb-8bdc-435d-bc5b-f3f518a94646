package utils

import (
	"net"
	"net/http"
	"strings"
)

func GenerateDeviceID() string {
	return GenerateRandomString(32)
}

func GetDeviceID(r *http.Request) string {
	cookie, err := r.<PERSON>("device_id")
	if err != nil {
		return ""
	}
	deviceID := cookie.Value
	if deviceID == "" {
		return ""
	}
	return deviceID
}

// getDeviceIP 获取设备 IP
func GetDeviceIP(r *http.Request) string {
	// 从 RemoteAddr 获取 （chi middleware RealIP）
	ip, _, err := net.SplitHostPort(r.RemoteAddr)
	if err != nil {
		return r.RemoteAddr
	}
	if ip != "" {
		return ip
	}

	// Fallback 尝试从 X-Real-IP 获取
	ip = r.Header.Get("X-Real-IP")
	if ip != "" {
		return ip
	}

	// 尝试从 X-Forwarded-For 获取
	forwarded := r.Header.Get("X-Forwarded-For")
	if forwarded != "" {
		i := strings.Index(forwarded, ",")
		if i == -1 {
			i = len(forwarded)
		}
		ip = forwarded[:i]
	}

	if ip == "" || net.ParseIP(ip) == nil {
		return ""
	}

	return ip
}

func GetDeviceUserAgent(r *http.Request) string {
	return r.UserAgent()
}

// DetectDeviceType 检测设备类型
func DetectDeviceType(userAgent string) string {
	ua := strings.ToLower(userAgent)

	switch {
	case strings.Contains(ua, "iphone") || strings.Contains(ua, "ipad"):
		return "iOS"
	case strings.Contains(ua, "android"):
		return "Android"
	case strings.Contains(ua, "windows phone"):
		return "Windows Phone"
	case strings.Contains(ua, "mac"):
		return "macOS"
	case strings.Contains(ua, "windows"):
		return "Windows"
	case strings.Contains(ua, "linux"):
		return "Linux"
	default:
		return "Unknown"
	}
}
