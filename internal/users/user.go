package users

import (
	"errors"
	"time"
)

// Role 用户角色
type Role string

const (
	RoleSuperAdmin Role = "super_admin" // 超级管理员角色
	RoleAdmin      Role = "admin"       // 管理员角色
	RoleUser       Role = "user"        // 普通用户角色
)

// Status 用户状态
type Status string

const (
	StatusActive  Status = "active"  // 激活状态
	StatusLocked  Status = "locked"  // 锁定状态
	StatusDeleted Status = "deleted" // 已删除状态
	StatusPending Status = "pending" // 待审核状态
	StatusBanned  Status = "banned"  // 封禁状态
)

// User 用户结构体
type User struct {
	ID        string                 `bson:"id" json:"id"`
	Username  string                 `bson:"username" json:"username"`     // 用户名用于登录
	Nickname  string                 `bson:"nickname" json:"nickname"`     // 昵称中文名
	Email     string                 `bson:"email" json:"email"`           // 邮箱地址
	AvatarURL string                 `bson:"avatar_url" json:"avatar_url"` // 头像URL
	Role      Role                   `bson:"role" json:"role"`             // 角色
	Status    Status                 `bson:"status" json:"status"`         // 用户状态
	Password  string                 `bson:"password" json:"-"`            // 不在JSON响应中返回密码
	Salt      string                 `bson:"salt" json:"-"`                // 不在JSON响应中返回盐值
	Provider  string                 `json:"provider"`                     // e.g., "feishu", "google"
	RawData   map[string]interface{} `json:"raw_data,omitempty"`           // Raw data from the provider
	CreatedAt time.Time              `bson:"created_at" json:"created_at"`
	UpdatedAt time.Time              `bson:"updated_at" json:"updated_at"`
}

// Validate 验证用户信息
func (u *User) Validate() error {
	// TODO: 添加更多验证逻辑
	if u == nil {
		return errors.New("用户信息不能为空")
	}
	return nil
}
