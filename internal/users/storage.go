package users

import (
	"context"
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"log"
	"time"

	"github.com/google/uuid"
)

// StorageBackend 用户存储接口
type StorageBackend interface {
	GetByID(ctx context.Context, id string) (*User, error)
	GetByUsername(ctx context.Context, username string) (*User, error)
	GetByEmail(ctx context.Context, email string) (*User, error)
	Create(ctx context.Context, user *User) error
	Update(ctx context.Context, user *User) error
	Delete(ctx context.Context, id string) error
	List(ctx context.Context, offset, limit int64) ([]*User, error)
	Count(ctx context.Context) (int64, error)
	HasSuperAdmin(ctx context.Context) (bool, error)
}

// Storage 用户服务
type Storage struct {
	backend StorageBackend
}

// NewStorage 创建用户服务
func NewStorage(backend StorageBackend) *Storage {
	return &Storage{
		backend: backend,
	}
}

// ValidateCredentials 验证用户凭据
func (s *Storage) ValidateCredentials(ctx context.Context, username, password string) (*User, error) {
	// 通过用户名获取用户
	user, err := s.backend.GetByUsername(ctx, username)
	if err != nil {
		return nil, err
	}
	if user == nil {
		// 也可以尝试通过邮箱登录
		user, err = s.backend.GetByEmail(ctx, username)
		if err != nil {
			return nil, err
		}
		if user == nil {
			return nil, fmt.Errorf("user not found")
		}
	}

	// 验证密码
	if !verifyPassword(password, user.Password, user.Salt) {
		return nil, fmt.Errorf("invalid password")
	}

	return user, nil
}

// HasSuperAdmin 检查是否存在超级管理员
func (s *Storage) HasSuperAdmin(ctx context.Context) (bool, error) {
	return s.backend.HasSuperAdmin(ctx)
}

// CreateSuperAdmin 创建超级管理员用户
func (s *Storage) CreateSuperAdmin(ctx context.Context, user *User) (*User, error) {
	// 检查是否已存在超级管理员
	hasSuperAdmin, err := s.backend.HasSuperAdmin(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to check super admin: %w", err)
	}
	if hasSuperAdmin {
		log.Println("Super admin already exists, skipping creation")
		return nil, nil
	}

	// 生成用户ID
	userID := uuid.New().String()

	// 生成默认密码和盐值
	password := generateRandomPassword()
	salt := generateSalt()
	hashedPassword := hashPassword(password, salt)

	user = &User{
		ID:        userID,
		Username:  user.Username,  // From original user parameter
		Nickname:  user.Nickname,  // From original user parameter
		Email:     user.Email,     // From original user parameter
		AvatarURL: user.AvatarURL, // From original user parameter
		Role:      RoleSuperAdmin, // Set role to super admin
		Status:    StatusActive,   // Set status to active
		Password:  hashedPassword, // Use the generated hashed password
		Salt:      salt,           // Store the salt
		Provider:  user.Provider,  // From original user parameter
		RawData:   user.RawData,   // From original user parameter
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	if err := s.backend.Create(ctx, user); err != nil {
		return nil, fmt.Errorf("failed to create super admin: %w", err)
	}

	log.Printf("Super admin created successfully: %s", user.Username) // DO NOT log passwords
	return user, nil
}

// RegisterOrUpdate 注册或更新用户（OAuth 登录时使用）
func (s *Storage) RegisterOrUpdate(ctx context.Context, oauthUser *User) (*User, error) {
	if oauthUser == nil {
		return nil, fmt.Errorf("oauth user cannot be nil")
	}

	// 检查用户是否已存在
	existingUser, err := s.backend.GetByUsername(ctx, oauthUser.Username)
	if err != nil {
		return nil, fmt.Errorf("failed to check existing user: %w", err)
	}
	if existingUser == nil {
		existingUser, err = s.backend.GetByEmail(ctx, oauthUser.Email)
		if err != nil {
			return nil, fmt.Errorf("failed to check existing user: %w", err)
		}
	}

	var user *User
	if existingUser == nil {
		// 用户不存在，创建新用户
		user = &User{
			ID:        uuid.New().String(),
			Username:  oauthUser.Username,
			Nickname:  oauthUser.Nickname,
			Email:     oauthUser.Email,
			AvatarURL: oauthUser.AvatarURL,
			Role:      RoleUser,      // 默认为普通用户
			Status:    StatusPending, // 默认为待审核状态
			Provider:  oauthUser.Provider,
			RawData:   oauthUser.RawData,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}

		if err := s.backend.Create(ctx, user); err != nil {
			return nil, fmt.Errorf("failed to create user: %w", err)
		}

		log.Printf("New user created: %s (%s)", user.Username, user.Email)
	} else {
		// 用户已存在，更新用户信息
		// 合并 RawData
		if oauthUser.Provider != "" && oauthUser.RawData[oauthUser.Provider] != nil {
			if existingUser.RawData == nil {
				existingUser.RawData = make(map[string]interface{})
			}
			existingUser.RawData[oauthUser.Provider] = oauthUser.RawData[oauthUser.Provider]
		}

		existingUser.Username = oauthUser.Username
		existingUser.Nickname = oauthUser.Nickname
		existingUser.Email = oauthUser.Email
		existingUser.AvatarURL = oauthUser.AvatarURL
		existingUser.Provider = oauthUser.Provider
		existingUser.RawData = oauthUser.RawData
		existingUser.UpdatedAt = time.Now()

		if err := s.backend.Update(ctx, existingUser); err != nil {
			return nil, fmt.Errorf("failed to update user: %w", err)
		}

		user = existingUser
		log.Printf("User [%s] updated", user.Username)
	}

	return user, nil
}

// GetByID 根据 ID 获取用户
func (s *Storage) GetByID(ctx context.Context, id string) (*User, error) {
	return s.backend.GetByID(ctx, id)
}

// GetByUsername 根据用户名获取用户
func (s *Storage) GetByUsername(ctx context.Context, username string) (*User, error) {
	user, err := s.backend.GetByUsername(ctx, username)
	if err != nil {
		return nil, err
	}
	if user == nil {
		// 也可以尝试通过邮箱登录
		user, err = s.backend.GetByEmail(ctx, username)
		if err != nil {
			return nil, err
		}
	}
	return user, nil
}

// generateRandomPassword 生成随机密码
func generateRandomPassword() string {
	bytes := make([]byte, 12)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)[:16]
}

// generateSalt 生成盐值
func generateSalt() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

// hashPassword 哈希密码
func hashPassword(password, salt string) string {
	hash := sha256.Sum256([]byte(password + salt))
	return hex.EncodeToString(hash[:])
}

// verifyPassword 验证密码是否正确
func verifyPassword(password, hashedPassword, salt string) bool {
	return hashedPassword == hashPassword(password, salt)
}
