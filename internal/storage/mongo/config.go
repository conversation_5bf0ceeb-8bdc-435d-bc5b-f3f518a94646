package mongo

import (
	"context"
	"fmt"
	"log"
	"sync"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"jiansec/hostaudit/internal/config"
)

const (
	CollectionConfig = "config"
	configDocumentID = "hostaudit-config" // 配置文档的固定 ID
)

// configBackend MongoDB 配置存储后端
type configBackend struct {
	coll   *mongo.Collection
	mu     sync.RWMutex
	cached *config.Config
}

// newConfigBackend 创建新的配置后端
func newConfigBackend(db *mongo.Database) *configBackend {
	return &configBackend{
		coll: db.Collection(CollectionConfig),
	}
}

// Get 获取配置
func (c *configBackend) Get(ctx context.Context) (*config.Config, error) {
	cfg := &config.Config{}

	filter := bson.M{"_id": configDocumentID}
	err := c.coll.FindOne(ctx, filter).Decode(cfg)

	if err != nil {
		if err == mongo.ErrNoDocuments || err == mongo.ErrNilDocument {
			// 没有找到配置文档，返回默认配置
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get config: %w", err)
	}

	return cfg, nil
}

// GetCached 获取缓存的配置
func (c *configBackend) GetCached(ctx context.Context) (*config.Config, error) {
	// 先尝试从缓存获取
	c.mu.RLock()
	if c.cached != nil {
		cfg := *c.cached
		c.mu.RUnlock()
		return &cfg, nil
	}
	c.mu.RUnlock()

	// 缓存未命中，从数据库获取
	cfg, err := c.Get(ctx)
	if err != nil {
		return nil, err
	}

	// 更新缓存
	c.mu.Lock()
	c.cached = cfg
	c.mu.Unlock()

	return cfg, nil
}

// Save 保存配置
func (c *configBackend) Save(ctx context.Context, cfg *config.Config) error {
	if cfg == nil {
		return fmt.Errorf("config cannot be nil")
	}

	// 验证配置
	if err := cfg.Validate(); err != nil {
		return fmt.Errorf("invalid config: %w", err)
	}

	// 准备更新选项
	filter := bson.M{"_id": configDocumentID}
	update := bson.M{"$set": cfg}
	opts := options.Update().SetUpsert(true)

	// 执行更新
	result, err := c.coll.UpdateOne(ctx, filter, update, opts)
	if err != nil {
		return fmt.Errorf("failed to save config: %w", err)
	}

	// 记录操作结果
	if result.UpsertedCount > 0 {
		log.Printf("Config document created with ID: %v\n", result.UpsertedID)
	} else if result.ModifiedCount > 0 {
		log.Println("Config document updated")
	}

	// 清除缓存
	c.ClearCache()

	return nil
}

// ClearCache 清除缓存
func (c *configBackend) ClearCache() {
	c.mu.Lock()
	c.cached = nil
	c.mu.Unlock()
}
