package mongo

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"jiansec/hostaudit/internal/users"
)

const (
	CollectionUsers = "users"
)

// usersBackend MongoDB 用户存储后端
type usersBackend struct {
	coll *mongo.Collection
}

// newUsersBackend 创建新的用户后端
func newUsersBackend(db *mongo.Database) *usersBackend {
	return &usersBackend{
		coll: db.Collection(CollectionUsers),
	}
}

// GetByID 根据 ID 获取用户
func (u *usersBackend) GetByID(ctx context.Context, id string) (*users.User, error) {
	if id == "" {
		return nil, fmt.Errorf("user ID cannot be empty")
	}

	var user users.User
	filter := bson.M{"id": id}
	err := u.coll.FindOne(ctx, filter).Decode(&user)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, nil // 用户不存在
		}
		return nil, fmt.Errorf("failed to get user by ID: %w", err)
	}

	return &user, nil
}

// GetByUsername 根据用户名获取用户
func (u *usersBackend) GetByUsername(ctx context.Context, username string) (*users.User, error) {
	if username == "" {
		return nil, fmt.Errorf("username cannot be empty")
	}

	var user users.User
	filter := bson.M{"username": username}
	err := u.coll.FindOne(ctx, filter).Decode(&user)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, nil // 用户不存在
		}
		return nil, fmt.Errorf("failed to get user by username: %w", err)
	}

	return &user, nil
}

// GetByEmail 根据邮箱获取用户
func (u *usersBackend) GetByEmail(ctx context.Context, email string) (*users.User, error) {
	if email == "" {
		return nil, fmt.Errorf("email cannot be empty")
	}

	var user users.User
	filter := bson.M{"email": email}
	err := u.coll.FindOne(ctx, filter).Decode(&user)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, nil // 用户不存在
		}
		return nil, fmt.Errorf("failed to get user by email: %w", err)
	}

	return &user, nil
}

// Create 创建用户
func (u *usersBackend) Create(ctx context.Context, user *users.User) error {
	if user == nil {
		return fmt.Errorf("user cannot be nil")
	}

	// 设置创建和更新时间
	now := time.Now()
	user.CreatedAt = now
	user.UpdatedAt = now

	// 检查用户是否已存在
	existingUser, err := u.GetByID(ctx, user.ID)
	if err != nil {
		return fmt.Errorf("failed to check existing user: %w", err)
	}
	if existingUser != nil {
		return fmt.Errorf("user with ID %s already exists", user.ID)
	}

	// 检查用户名是否已存在
	if user.Username != "" {
		existingUser, err = u.GetByUsername(ctx, user.Username)
		if err != nil {
			return fmt.Errorf("failed to check existing username: %w", err)
		}
		if existingUser != nil {
			return fmt.Errorf("user with username %s already exists", user.Username)
		}
	}

	// 检查邮箱是否已存在
	if user.Email != "" {
		existingUser, err = u.GetByEmail(ctx, user.Email)
		if err != nil {
			return fmt.Errorf("failed to check existing email: %w", err)
		}
		if existingUser != nil {
			return fmt.Errorf("user with email %s already exists", user.Email)
		}
	}

	// 插入用户
	_, err = u.coll.InsertOne(ctx, user)
	if err != nil {
		return fmt.Errorf("failed to create user: %w", err)
	}

	return nil
}

// Update 更新用户
func (u *usersBackend) Update(ctx context.Context, user *users.User) error {
	if user == nil {
		return fmt.Errorf("user cannot be nil")
	}
	if user.ID == "" {
		return fmt.Errorf("user ID cannot be empty")
	}

	// 设置更新时间
	user.UpdatedAt = time.Now()

	filter := bson.M{"id": user.ID}
	update := bson.M{"$set": user}

	result, err := u.coll.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update user: %w", err)
	}

	if result.MatchedCount == 0 {
		return fmt.Errorf("user with ID %s not found", user.ID)
	}

	return nil
}

// Delete 删除用户
func (u *usersBackend) Delete(ctx context.Context, id string) error {
	if id == "" {
		return fmt.Errorf("user ID cannot be empty")
	}

	filter := bson.M{"id": id}
	result, err := u.coll.DeleteOne(ctx, filter)
	if err != nil {
		return fmt.Errorf("failed to delete user: %w", err)
	}

	if result.DeletedCount == 0 {
		return fmt.Errorf("user with ID %s not found", id)
	}

	return nil
}

// List 列出用户（分页）
func (u *usersBackend) List(ctx context.Context, offset, limit int64) ([]*users.User, error) {
	if offset < 0 {
		offset = 0
	}
	if limit <= 0 {
		limit = 50 // 默认限制
	}

	opts := options.Find().
		SetSkip(offset).
		SetLimit(limit).
		SetSort(bson.M{"created_at": -1}) // 按创建时间倒序

	cursor, err := u.coll.Find(ctx, bson.M{}, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to list users: %w", err)
	}
	defer cursor.Close(ctx)

	var userList []*users.User
	for cursor.Next(ctx) {
		var user users.User
		if err := cursor.Decode(&user); err != nil {
			return nil, fmt.Errorf("failed to decode user: %w", err)
		}
		userList = append(userList, &user)
	}

	if err := cursor.Err(); err != nil {
		return nil, fmt.Errorf("cursor error: %w", err)
	}

	return userList, nil
}

// Count 统计用户数量
func (u *usersBackend) Count(ctx context.Context) (int64, error) {
	count, err := u.coll.CountDocuments(ctx, bson.M{})
	if err != nil {
		return 0, fmt.Errorf("failed to count users: %w", err)
	}
	return count, nil
}

// HasSuperAdmin 检查是否存在超级管理员
func (u *usersBackend) HasSuperAdmin(ctx context.Context) (bool, error) {
	filter := bson.M{"role": users.RoleSuperAdmin}
	count, err := u.coll.CountDocuments(ctx, filter)
	if err != nil {
		return false, fmt.Errorf("failed to check super admin: %w", err)
	}
	return count > 0, nil
}
