package config

import (
	"errors"
	"fmt"
	"log"
	"os"
	"path"
	"strings"

	_ "github.com/joho/godotenv/autoload"
	"github.com/mitchellh/go-homedir"
	"github.com/spf13/cobra"
	"github.com/spf13/pflag"
	"github.com/spf13/viper"
)

var (
	v       = viper.New()
	cfgFile string
	rootCmd *cobra.Command
)

// Load 加载配置
func Load() (*Config, error) {
	// 初始化命令
	rootCmd = setupCommand()

	// 设置命令行参数但不执行命令
	rootCmd.RunE = func(cmd *cobra.Command, args []string) error {
		log.Println(cfgFile)
		return nil
	}

	// 解析命令行参数
	if err := rootCmd.Execute(); err != nil {
		return nil, fmt.Errorf("failed to parse flags: %w", err)
	}

	// 构建配置
	return buildConfig(rootCmd.Flags())
}

// setupCommand 设置命令
func setupCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "hostaudit",
		Short:   "A stylish web-based host auditing",
		Long:    getLongDescription(),
		Version: "1.0.0",
	}

	cobra.OnInitialize(initConfig)
	setupFlags(cmd)

	return cmd
}

// setupFlags 设置命令行标志
func setupFlags(cmd *cobra.Command) {
	flags := cmd.Flags()
	persistent := cmd.PersistentFlags()

	// 持久化标志
	persistent.StringVarP(&cfgFile, "config", "c", "", "config file (default is ./config.yml)")

	// 普通标志
	flags.String("env", DefaultEnv, "environment (development, staging, production)")
	flags.StringP("redis.uri", "r", "", "Redis URI (required)")
	flags.StringP("mongo.uri", "m", "", "MongoDB URI (required)")
	flags.StringP("jwt.secret", "s", "", "JWT secret key (required)")
	flags.StringP("server.addr", "a", DefaultServerAddr, "server address")
	flags.StringP("server.port", "p", DefaultServerPort, "server port")
	flags.StringP("server.log", "l", DefaultServerLog, "log output (stdout, stderr, or file path)")
	flags.StringP("jwt.expiry", "e", DefaultJWTExpiry, "JWT expiry time in seconds")

	// OAuth 相关
	flags.String("oauth.feishu.client_id", "", "Feishu OAuth client ID")
	flags.String("oauth.feishu.client_secret", "", "Feishu OAuth client secret")
	flags.String("oauth.feishu.redirect_uri", "", "Feishu OAuth redirect URI")
}

// initConfig 初始化配置
func initConfig() {
	if cfgFile != "" {
		// 使用指定的配置文件
		v.SetConfigFile(cfgFile)
	} else {
		// 搜索配置文件
		home, err := homedir.Dir()
		if err != nil {
			fmt.Fprintf(os.Stderr, "Failed to get home directory: %v\n", err)
		}

		// 添加搜索路径
		v.AddConfigPath(".")
		v.AddConfigPath("./.hostaudit")
		if home != "" {
			v.AddConfigPath(path.Join(home, ".hostaudit"))
		}
		v.AddConfigPath("/etc/hostaudit/")

		v.SetConfigName("config")
		v.SetConfigType("yml")
	}

	// 设置环境变量
	v.SetEnvPrefix("HOSTAUDIT")
	v.AutomaticEnv()
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "_", "-", "_"))

	// 读取配置文件
	if err := v.ReadInConfig(); err != nil {
		var configFileNotFoundError viper.ConfigFileNotFoundError
		if !errors.As(err, &configFileNotFoundError) {
			// 配置文件解析错误
			fmt.Fprintf(os.Stderr, "Failed to read config file: %v\n", err)
		}
		cfgFile = "No config file used"
	} else {
		cfgFile = "Using config file: " + v.ConfigFileUsed()
	}
}

// buildConfig 构建配置对象
func buildConfig(flags *pflag.FlagSet) (*Config, error) {
	cfg := &Config{
		Env:           getParam(flags, "env"),
		RedisURI:      getParam(flags, "redis.uri"),
		MongoURI:      getParam(flags, "mongo.uri"),
		SessionSecret: getParam(flags, "session.secret"),
		JWTSecret:     getParam(flags, "jwt.secret"),
		ServerAddr:    getParam(flags, "server.addr"),
		ServerPort:    getParam(flags, "server.port"),
		ServerLog:     getParam(flags, "server.log"),
		JWTExpiry:     getParam(flags, "jwt.expiry"),
		OAuthFeishu: OAuth{
			ClientID:     os.Getenv("HOSTAUDIT_OAUTH_FEISHU_CLIENT_ID"),
			ClientSecret: os.Getenv("HOSTAUDIT_OAUTH_FEISHU_CLIENT_SECRET"),
			RedirectURI:  os.Getenv("HOSTAUDIT_OAUTH_FEISHU_REDIRECT_URI"),
		},
	}

	// 验证配置
	if err := cfg.Validate(); err != nil {
		return nil, fmt.Errorf("invalid configuration: %w", err)
	}

	return cfg, nil
}

// getParamB 获取参数值，优先级：命令行 > 环境变量 > 配置文件 > 默认值
func getParamB(flags *pflag.FlagSet, key string) (string, bool) {
	value, _ := flags.GetString(key)

	// 如果在命令行标志中设置了该参数，则返回它。
	if flags.Changed(key) {
		return value, true
	}

	// 如果在 viper 中设置了该参数（环境变量或配置文件），则返回它。
	if v.IsSet(key) {
		return v.GetString(key), true
	}

	// 否则，返回默认值。
	return value, false
}

func getParam(flags *pflag.FlagSet, key string) string {
	val, _ := getParamB(flags, key)
	return val
}

// getLongDescription 获取详细描述
func getLongDescription() string {
	return `Host Audit is a web-based host auditing system.

This CLI allows you to manage the Host Audit server, including:
- Database initialization
- User management  
- Configuration management

Configuration can be provided through:
1. Command line flags (highest priority)
2. Environment variables (HOSTAUDIT_*)
3. Configuration file
4. Default values (lowest priority)

The configuration file is searched in the following locations:
- ./config.yml
- ./.hostaudit/config.yml
- $HOME/.hostaudit/config.yml
- /etc/hostaudit/config.yml

Example:
  hostaudit --mongo.uri "mongodb://localhost:27017/hostaudit" --jwt.secret "mysecret"
  
Or using environment variables:
  export HOSTAUDIT_MONGO_URI="mongodb://localhost:27017/hostaudit"
  export HOSTAUDIT_JWT_SECRET="mysecret"
  hostaudit`
}
