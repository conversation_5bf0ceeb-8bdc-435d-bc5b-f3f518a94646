{"name": "web", "private": true, "version": "0.0.0", "type": "module", "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource/roboto": "^5.2.5", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@mui/x-charts": "^8.3.1", "@mui/x-date-pickers": "^8.3.1", "@tabler/icons-react": "^3.33.0", "@tanstack/query-broadcast-client-experimental": "^5.76.0", "@tanstack/react-form": "^1.11.1", "@tanstack/react-query": "^5.76.0", "@tanstack/react-router": "^1.120.3", "@tanstack/react-table": "^8.21.3", "@tanstack/virtual-file-routes": "^1.115.0", "@tanstack/zod-adapter": "^1.120.5", "axios": "^1.9.0", "date-fns": "^4.1.0", "jwt-decode": "^4.0.0", "lodash-es": "^4.17.21", "notistack": "^3.0.2", "nprogress": "^0.2.0", "react": "^19.0.0", "react-device-detect": "^2.2.3", "react-dom": "^19.0.0", "simplebar-react": "^3.3.1", "sonner": "^2.0.3", "uuid": "^11.1.0", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.21.0", "@stylistic/eslint-plugin": "^4.2.0", "@tailwindcss/postcss": "^4.1.6", "@tailwindcss/vite": "^4.1.6", "@tanstack/eslint-config": "^0.1.0", "@tanstack/eslint-plugin-query": "^5.74.7", "@tanstack/eslint-plugin-router": "^1.115.0", "@tanstack/react-query-devtools": "^5.76.0", "@tanstack/react-router-devtools": "^1.120.3", "@tanstack/router-plugin": "^1.120.3", "@types/lodash-es": "^4.17.12", "@types/node": "^22.15.17", "@types/nprogress": "^0.2.3", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "clsx": "^2.1.1", "eslint": "^9.21.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "eslint-plugin-simple-import-sort": "^12.1.1", "globals": "^15.15.0", "prettier-plugin-tailwindcss": "^0.6.11", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.6", "tw-animate-css": "^1.2.9", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0", "vite-tsconfig-paths": "^5.1.4", "zod": "^3.24.4"}}