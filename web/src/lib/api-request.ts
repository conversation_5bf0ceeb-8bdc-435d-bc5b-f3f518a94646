/* eslint-disable @typescript-eslint/no-explicit-any */
import axios, { type AxiosResponse, type InternalAxiosRequestConfig, isAxiosError } from 'axios'
import { toast } from 'sonner'
import { v4 as uuidv4 } from 'uuid'
import { z, ZodError } from 'zod'

import { API_TOAST_BLACKLIST_URLS } from '@/constants/api-toast-urls'
import { ACCESS_TOKEN_KEY, BASE_URL, DEVICE_ID_KEY } from '@/env'
import * as progress from '@/lib/progress'
import type { RecordValueUnion } from '@/types/utility'
import { getLocalStorage, removeLocalStorage, setLocalStorage } from './storage'

declare module 'axios' {
  export interface AxiosInstance {
    request<T = any, D = any>(config: AxiosRequestConfig<D>): Promise<T>
    get<T = any, D = any>(url: string, config?: AxiosRequestConfig<D>): Promise<T>
    delete<T = any, D = any>(url: string, config?: AxiosRequestConfig<D>): Promise<T>
    head<T = any, D = any>(url: string, config?: AxiosRequestConfig<D>): Promise<T>
    options<T = any, D = any>(url: string, config?: AxiosRequestConfig<D>): Promise<T>
    post<T = any, D = any>(url: string, data?: D, config?: AxiosRequestConfig<D>): Promise<T>
    put<T = any, D = any>(url: string, data?: D, config?: AxiosRequestConfig<D>): Promise<T>
    patch<T = any, D = any>(url: string, data?: D, config?: AxiosRequestConfig<D>): Promise<T>
  }
}

export enum ApiStatus {
  Success = 2000,
  Unauthorized = 401,
  Error = 200,
  Expiration = 600,
  RulesExpiration = 700,
  SystemError = 500
}

export const ApiStatusDesc = {
  [ApiStatus.Success]: '',
  [ApiStatus.Unauthorized]: '未授权 [401]',
  [ApiStatus.Error]: '请求失败 [200]',
  [ApiStatus.Expiration]: '登录过期 [600]',
  [ApiStatus.RulesExpiration]: '规则过期 [700]',
  [ApiStatus.SystemError]: '系统错误 [500]'
}

// Response Schema
export const PaginationSchema = z.object({
  total: z.number(),
  page: z.number(),
  page_size: z.number()
})

export const wrapPaginationSchema = <T extends z.ZodRawShape>(dataSchema: z.ZodObject<T>) =>
  dataSchema.extend({
    pagination: PaginationSchema
  })

export const createApiResponseSchema = <T extends z.ZodTypeAny>(dataSchema: T) =>
  z.object({
    data: dataSchema,
    meta: z.object({
      status: z.nativeEnum(ApiStatus),
      msg: z
        .string()
        .transform(value => {
          if (value === 'null') return ''
          return value
        })
        .default('')
    })
  })

export const ApiResponseSchema = createApiResponseSchema(z.any())
export type ApiResponse<T = any> = z.infer<typeof ApiResponseSchema> & { data?: T }
export type Pagination = z.infer<typeof PaginationSchema>
export type ApiStatusUnion = RecordValueUnion<typeof ApiStatus>
// API 响应错误
export class ApiResponseError extends Error {
  status: ApiStatus
  msg: string
  isApiResponseError = true

  constructor(status: ApiStatus, message: string) {
    super(`${message || '请联系开发者'}`)
    this.name = 'ApiResponseError'
    this.status = status
    this.msg = message
  }
}

// 新增的未验证身份错误类
export class UnauthorizedError extends ApiResponseError {
  constructor(message = '用户未登录或登录已过期，请重新登录') {
    // 通常未授权错误的状态码是 401
    super(ApiStatus.Unauthorized, message)
    this.name = 'UnauthorizedError'
  }
}

export class ExpirationError extends ApiResponseError {
  constructor(message = '服务过期, 请联系管理员') {
    super(ApiStatus.Expiration, message)
    this.name = 'ExpirationError'
  }
}

// API 响应数据验证错误
export class ApiResponseDataError extends Error {
  fieldErrors: ZodError<any>
  isApiResponseDataError = true

  constructor(errors: ZodError<any>) {
    super('API Response Data Validation Error' + errors.message)
    this.name = 'ApiResponseDataError'
    this.fieldErrors = errors
  }
}

// API 请求参数错误
export class ApiRequestDataError extends Error {
  fieldErrors: ZodError<any>
  isApiRequestDataError = true

  constructor(errors: ZodError<any>) {
    super('API Request Data Validation Error')
    this.name = 'ApiRequestDataError'
    this.fieldErrors = errors
  }
}

export function isApiResponseError(payload: any): payload is ApiResponseError {
  return payload instanceof ApiResponseError
}
export function isApiResponseDataError(payload: any): payload is ApiResponseDataError {
  return payload instanceof ApiResponseDataError
}
export function isApiRequestDataError(payload: any): payload is ApiRequestDataError {
  return payload instanceof ApiRequestDataError
}

export const apiRequest = axios.create({
  baseURL: BASE_URL,
  timeout: 300 * 1000,
  withCredentials: true
})

apiRequest.interceptors.request.use(onRequest, onRequestError)
apiRequest.interceptors.response.use(onResponse, onResponseError)

const deviceID = getLocalStorage(DEVICE_ID_KEY)
if (!deviceID) {
  setLocalStorage(DEVICE_ID_KEY, uuidv4())
}

// Request Interceptor
function onRequest(config: InternalAxiosRequestConfig) {
  progress.start(config.url)
  // Add token to headers if available
  const accessToken = getLocalStorage(ACCESS_TOKEN_KEY)
  if (accessToken) {
    config.headers['Authorization'] = `Bearer ${accessToken}`
  }

  const deviceID = getLocalStorage(DEVICE_ID_KEY)
  if (deviceID) {
    config.headers['X-Device-ID'] = deviceID
  }
  return config
}

// Request Error Interceptor
function onRequestError(error: any) {
  progress.done(error.config.url)
  return Promise.reject(error)
}

// Response Interceptor
function onResponse(response: AxiosResponse<ApiResponse>) {
  const validationApiResponse = ApiResponseSchema.safeParse(response.data)
  if (!validationApiResponse.success) {
    toastError('后台数据错误', '请联系开发者')
    throw new ApiResponseDataError(validationApiResponse.error)
  }
  const apiResponse = validationApiResponse.data
  const { meta } = apiResponse
  const { status, msg } = meta
  progress.done(response.config.url)
  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
  toastApiResponse(apiResponse, response.config.url!)

  // 需要清除 Token 并提示重新登录的情况
  if (status === ApiStatus.Unauthorized) {
    removeLocalStorage(ACCESS_TOKEN_KEY)
    throw new UnauthorizedError(msg)
  }
  if (status === ApiStatus.Expiration) {
    removeLocalStorage(ACCESS_TOKEN_KEY)
    throw new ExpirationError(msg)
  }

  if (status !== ApiStatus.Success) {
    throw new ApiResponseError(status, msg)
  }

  return apiResponse as any
}

// Response Error Interceptor
function onResponseError(error: any) {
  progress.done(error.url)
  if (isAxiosError(error)) {
    toastError(`AxiosError ${error.status || ''}`, error.message)
  } else {
    toastError('未知错误', '请联系开发者')
    console.error('未知错误', error)
  }
  return Promise.reject(error)
}

function resolveApiResponseMsg(response: ApiResponse, defaultMsg = '') {
  return response.meta.msg || defaultMsg
}

function toastError(title: string, message: string) {
  toast.error(title, {
    description: message
  })
}

// 只有当message存在时才会显示
function toastSuccess(_title?: string, message?: string) {
  if (message) {
    toast.success(message)
  }
}

// TODO
// eslint-disable-next-line @typescript-eslint/no-empty-function
const noop: (...args: any[]) => void = () => {}

export function toastApiResponse(response: ApiResponse, url: string) {
  const status = response.meta.status
  const toastSuccess_ = API_TOAST_BLACKLIST_URLS.includes(url) ? noop : toastSuccess
  const toastFn = status === ApiStatus.Success ? toastSuccess_ : toastError
  const title = ApiStatusDesc[status]
  const message =
    status === ApiStatus.Success
      ? resolveApiResponseMsg(response)
      : resolveApiResponseMsg(response, '请联系开发者')
  toastFn(title, message)
}
