import type { FileRouteTypes } from '@/routeTree.gen'

export type RoutePath = FileRouteTypes['fullPaths']
export type RouteId = FileRouteTypes['id']

const setRoute = <TFull extends FileRouteTypes['fullPaths'], TId extends FileRouteTypes['id']>(
  path: TFull,
  id: TId
) => ({ path, id }) as const

export const ROUTE_PATHS = Object.freeze({
  AUTH_LOGIN_PASSWORD: setRoute('/auth/login-password', '/auth/_auth-layout/login-password'),
  AUTH_LOGIN_FEISHU: setRoute('/auth/login-feishu', '/auth/_auth-layout/login-feishu'),

  // Authenticated
  INDEX: setRoute('/', '/_auth/_role/_authenticated-layout/'),
  PROJECTS: setRoute('/projects', '/_auth/_role/_authenticated-layout/projects'),

  // Admin only
  ADMIN_DASHBOARD: setRoute('/dashboard', '/_auth/_role/_admin-layout/dashboard'),
  USER_MANAGEMENT: setRoute('/users', '/_auth/_role/_admin-layout/users')
})
