import { createFileRoute, Navigate } from '@tanstack/react-router'

import { ROUTE_PATHS } from '@/constants/routes'
import { AuthRole } from '@/hooks/api/user'
import { useCurrentUser } from '@/hooks/use-current-user'

const Index = () => {
  const { user } = useCurrentUser()

  if (user.role === AuthRole.ADMIN || user.role === AuthRole.SUPER_ADMIN) {
    return <Navigate to={ROUTE_PATHS.ADMIN_DASHBOARD.path} replace />
  }

  return <Navigate to={ROUTE_PATHS.PROJECTS.path} replace />
}

export const Route = createFileRoute('/_auth/_role/_authenticated-layout/')({
  component: Index
})
