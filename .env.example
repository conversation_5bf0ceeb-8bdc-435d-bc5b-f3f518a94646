# MongoDB 初始化配置 (Docker)
MONGO_INITDB_DATABASE=hostaudit
MONGO_INITDB_ROOT_USERNAME=admin
MONGO_INITDB_ROOT_PASSWORD=password

# ENV 环境变量
HOSTAUDIT_ENV=production #（"development", "production"）
# MongoDB 连接配置
HOSTAUDIT_MONGO_URI=*******************************************************************
# Session 配置
HOSTAUDIT_SESSION_SECRET=your_session_secret_key
# JWT 配置
HOSTAUDIT_JWT_SECRET=your_jwt_secret_key
# OAuth Feishu 配置
HOSTAUDIT_OAUTH_FEISHU_CLIENT_ID=feishu_client_id
HOSTAUDIT_OAUTH_FEISHU_CLIENT_SECRET=feishu_client_secret
# OAuth Feishu 重定向 URI 注意后面的 redirect_uri 参数
HOSTAUDIT_OAUTH_FEISHU_REDIRECT_URI='http://localhost:3000/_api/oauth/feishu/callback?redirect_uri=http://localhost:3000/auth/login-feishu'
